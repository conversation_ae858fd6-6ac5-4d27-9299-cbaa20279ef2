# PDF Preview Canvas Manager

A standalone application for arranging PNG preview images on a canvas and exporting them to PDF format. Perfect for creating presentation layouts from your plotting application's preview images.

## Features

### Core Functionality
- **Drag and Drop**: Click and drag PNG images around the canvas
- **Resizable Items**: Select items to show resize handles, drag handles to resize
- **Multi-Selection**: Click items to select, Ctrl+click for multiple selection
- **Canvas Navigation**: Scrollable canvas with zoom capabilities

### File Operations
- **Add PNG Files**: Load individual PNG files from your file system
- **Load Project Previews**: Automatically find and load all `*_preview.png` files from your project directory structure
- **Clear Canvas**: Remove all items from the canvas

### Layout Tools
- **Auto Arrange**: Automatically arrange items in a grid layout
- **Align Left**: Align selected items to the leftmost position
- **Align Top**: Align selected items to the topmost position
- **Selection Tools**: Select all, deselect all, delete selected items

### Export and Save
- **Export to PDF**: Export the current canvas layout to a PDF file with proper scaling
- **Save Layout**: Save the current arrangement to a JSON file
- **Load Layout**: Restore a previously saved layout

## Installation

### Requirements
```bash
pip install Pillow reportlab
```

### Optional Dependencies
- **Pillow (PIL)**: Required for image loading and display
- **ReportLab**: Required for PDF export functionality

## Usage

### Basic Usage
1. Run the application:
   ```bash
   python PDFPreviewCanvas.py
   ```

2. Load images using one of these methods:
   - **Individual Files**: Click "Add PNG Files" to select specific PNG files
   - **Project Structure**: Click "Load Project Previews" to automatically load all preview images from a project directory

3. Arrange your images:
   - **Move**: Click and drag items around the canvas
   - **Resize**: Select an item to show blue resize handles, then drag the handles
   - **Select Multiple**: Hold Ctrl while clicking to select multiple items
   - **Auto Layout**: Use "Auto Arrange" for automatic grid layout

4. Export your layout:
   - **PDF Export**: Click "Export to PDF" to create a PDF with your layout
   - **Save Layout**: Save your arrangement for later use

### Integration with Plotting Applications

This tool is designed to work with the plotting application's project structure:

```
ProjectDirectory/
├── Project_A/
│   ├── Measurement_1/
│   │   ├── Measurement_1_preview.png  ← Loaded automatically
│   │   ├── plot_config.json
│   │   └── Data/
│   └── Measurement_2/
│       ├── Measurement_2_preview.png  ← Loaded automatically
│       └── ...
└── Project_B/
    └── ...
```

### Keyboard and Mouse Controls

- **Left Click**: Select item
- **Ctrl + Left Click**: Add item to selection
- **Left Click + Drag**: Move selected items
- **Double Click**: Edit item (placeholder for future functionality)
- **Resize Handles**: Drag blue squares to resize selected items

## Testing

Run the test script to create sample images and demonstrate functionality:

```bash
python test_pdf_canvas.py
```

This will:
1. Create sample PNG preview images
2. Create a sample project directory structure
3. Launch the application with test data loaded

## File Formats

### Layout Files (JSON)
The application saves layouts in JSON format:

```json
{
  "canvas_size": {
    "width": 800,
    "height": 600
  },
  "items": [
    {
      "image_path": "/path/to/image.png",
      "x": 50,
      "y": 50,
      "width": 200,
      "height": 150
    }
  ]
}
```

### PDF Export
- Automatically scales layout to fit PDF page (A4 size)
- Maintains aspect ratios of images
- Preserves relative positioning

## Architecture

### Main Classes

- **PDFPreviewCanvas**: Main application class handling UI and coordination
- **PreviewItem**: Represents individual PNG items on the canvas with position, size, and selection state

### Key Features Implementation

- **Drag and Drop**: Mouse event handling with coordinate tracking
- **Resize Functionality**: Corner handles with proportional resizing
- **Selection Management**: Visual feedback with blue selection rectangles
- **Canvas Scrolling**: Tkinter Canvas with scrollbars for large layouts
- **PDF Export**: ReportLab integration with automatic scaling

## Future Enhancements

- Grid snapping for precise alignment
- Zoom in/out functionality
- Undo/redo operations
- Copy/paste items
- Layer management
- Custom page sizes for PDF export
- Integration with plotting application for direct editing

## Troubleshooting

### Common Issues

1. **Images not loading**: Ensure PIL (Pillow) is installed
2. **PDF export not working**: Ensure ReportLab is installed
3. **Performance with many images**: Consider reducing image sizes or using thumbnails

### Error Messages

- "PIL not available": Install Pillow with `pip install Pillow`
- "ReportLab not available": Install ReportLab with `pip install reportlab`
- "No preview images found": Check that your project directory contains `*_preview.png` files
