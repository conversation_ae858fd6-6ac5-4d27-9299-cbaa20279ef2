#!/usr/bin/env python3
"""
Simple test to verify ReportLab installation and functionality
"""

def test_reportlab():
    try:
        # Test basic imports
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter, A4
        from reportlab.lib.units import inch
        print("✓ ReportLab imports successful")
        
        # Test creating a simple PDF
        test_file = "test_reportlab_output.pdf"
        c = canvas.Canvas(test_file, pagesize=letter)
        c.drawString(100, 750, "ReportLab Test - PDF Export Working!")
        c.save()
        print(f"✓ PDF creation successful: {test_file}")
        
        return True
        
    except ImportError as e:
        print(f"✗ ReportLab import failed: {e}")
        return False
    except Exception as e:
        print(f"✗ ReportLab test failed: {e}")
        return False

if __name__ == "__main__":
    print("Testing ReportLab installation...")
    success = test_reportlab()
    if success:
        print("\n✓ ReportLab is working correctly!")
        print("PDF export functionality should work in PDFPreviewCanvas.py")
    else:
        print("\n✗ ReportLab test failed!")
        print("You may need to install ReportLab with: pip install reportlab")
