#!/usr/bin/env python3
"""
PDF Preview Canvas Manager
A standalone script for arranging PNG previews on a PDF-like canvas with drag, resize, and selection functionality.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import json
from pathlib import Path

try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("Warning: PIL not available. Image functionality will be limited.")

try:
    from reportlab.pdfgen import canvas as pdf_canvas
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.lib.utils import ImageReader
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    print("Warning: ReportLab not available. PDF export functionality will be limited.")


class PreviewItem:
    """Represents a PNG preview item on the canvas"""
    
    def __init__(self, canvas, image_path, x=50, y=50, width=200, height=150):
        self.canvas = canvas
        self.image_path = image_path
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.selected = False
        self.canvas_id = None
        self.image_id = None
        self.selection_rect_id = None
        self.resize_handles = []
        self.original_image = None
        self.display_image = None
        
        # Load and display the image
        self.load_image()
        self.create_canvas_item()
    
    def load_image(self):
        """Load the PNG image"""
        if not PIL_AVAILABLE:
            return
        
        try:
            self.original_image = Image.open(self.image_path)
            self.update_display_image()
        except Exception as e:
            print(f"Error loading image {self.image_path}: {e}")
    
    def update_display_image(self):
        """Update the display image based on current size"""
        if not self.original_image:
            return

        # Ensure dimensions are integers
        width = int(round(self.width))
        height = int(round(self.height))

        # Resize image to fit current dimensions
        resized_image = self.original_image.resize((width, height), Image.Resampling.LANCZOS)
        self.display_image = ImageTk.PhotoImage(resized_image)
    
    def create_canvas_item(self):
        """Create the canvas item"""
        if self.display_image:
            self.image_id = self.canvas.create_image(
                self.x, self.y, anchor="nw", image=self.display_image
            )
        else:
            # Fallback rectangle if image can't be loaded
            self.image_id = self.canvas.create_rectangle(
                self.x, self.y, self.x + self.width, self.y + self.height,
                fill="lightgray", outline="black"
            )
            # Add text label
            self.canvas.create_text(
                self.x + self.width//2, self.y + self.height//2,
                text=os.path.basename(self.image_path), anchor="center"
            )
    
    def move_to(self, x, y):
        """Move the item to new coordinates"""
        dx = x - self.x
        dy = y - self.y
        self.x = x
        self.y = y
        
        if self.image_id:
            self.canvas.coords(self.image_id, self.x, self.y)
        
        self.update_selection_display()
        self.update_resize_handles()
    
    def resize_to(self, width, height):
        """Resize the item"""
        self.width = max(50, width)  # Minimum size
        self.height = max(50, height)
        
        # Update display image
        self.update_display_image()
        
        # Recreate canvas item with new image
        if self.image_id:
            self.canvas.delete(self.image_id)
        self.create_canvas_item()
        
        self.update_selection_display()
        self.update_resize_handles()
    
    def set_selected(self, selected):
        """Set selection state"""
        self.selected = selected
        self.update_selection_display()
        
        if selected:
            self.show_resize_handles()
        else:
            self.hide_resize_handles()
    
    def update_selection_display(self):
        """Update selection rectangle"""
        if self.selection_rect_id:
            self.canvas.delete(self.selection_rect_id)
            self.selection_rect_id = None
        
        if self.selected:
            self.selection_rect_id = self.canvas.create_rectangle(
                self.x - 2, self.y - 2, 
                self.x + self.width + 2, self.y + self.height + 2,
                outline="blue", width=2, fill=""
            )
    
    def show_resize_handles(self):
        """Show resize handles"""
        self.hide_resize_handles()
        
        handle_size = 8
        positions = [
            (self.x - handle_size//2, self.y - handle_size//2),  # Top-left
            (self.x + self.width - handle_size//2, self.y - handle_size//2),  # Top-right
            (self.x - handle_size//2, self.y + self.height - handle_size//2),  # Bottom-left
            (self.x + self.width - handle_size//2, self.y + self.height - handle_size//2),  # Bottom-right
        ]
        
        for i, (hx, hy) in enumerate(positions):
            handle = self.canvas.create_rectangle(
                hx, hy, hx + handle_size, hy + handle_size,
                fill="blue", outline="darkblue", width=1
            )
            self.resize_handles.append((handle, i))
    
    def hide_resize_handles(self):
        """Hide resize handles"""
        for handle, _ in self.resize_handles:
            self.canvas.delete(handle)
        self.resize_handles.clear()
    
    def update_resize_handles(self):
        """Update resize handle positions"""
        if self.resize_handles:
            self.show_resize_handles()
    
    def contains_point(self, x, y):
        """Check if point is within item bounds"""
        return (self.x <= x <= self.x + self.width and 
                self.y <= y <= self.y + self.height)
    
    def get_resize_handle_at(self, x, y):
        """Get resize handle at given coordinates"""
        for handle, handle_type in self.resize_handles:
            coords = self.canvas.coords(handle)
            if len(coords) >= 4:
                if coords[0] <= x <= coords[2] and coords[1] <= y <= coords[3]:
                    return handle_type
        return None
    
    def cleanup(self):
        """Clean up canvas items"""
        if self.image_id:
            self.canvas.delete(self.image_id)
        if self.selection_rect_id:
            self.canvas.delete(self.selection_rect_id)
        self.hide_resize_handles()


class PDFPreviewCanvas:
    """Main application class for PDF Preview Canvas Manager"""
    
    def __init__(self, parent_window=None, project_overview=None):
        if parent_window:
            self.window = parent_window
        else:
            self.window = tk.Tk()
            self.window.title("PDF Preview Canvas Manager")
            self.window.geometry("1200x800")

        # Reference to project overview for editing functionality
        self.project_overview = project_overview
        
        # Canvas settings
        self.canvas_width = 800
        self.canvas_height = 600
        
        # State variables
        self.preview_items = []
        self.selected_items = []
        self.drag_data = {"item": None, "x": 0, "y": 0}
        self.resize_data = {"item": None, "handle": None, "start_x": 0, "start_y": 0}

        # Grid settings
        self.show_grid = False
        self.grid_size = 20
        self.snap_to_grid = False
        
        self.setup_ui()
        self.bind_events()

    def snap_to_grid(self, x, y):
        """Snap coordinates to grid if snap is enabled"""
        if not self.snap_to_grid:
            return x, y

        snapped_x = round(x / self.grid_size) * self.grid_size
        snapped_y = round(y / self.grid_size) * self.grid_size
        return snapped_x, snapped_y

    def draw_grid(self):
        """Draw grid on canvas if enabled"""
        if not self.show_grid:
            return

        # Remove existing grid
        self.canvas.delete("grid")

        # Get visible area
        x1 = self.canvas.canvasx(0)
        y1 = self.canvas.canvasy(0)
        x2 = self.canvas.canvasx(self.canvas.winfo_width())
        y2 = self.canvas.canvasy(self.canvas.winfo_height())

        # Draw vertical lines
        start_x = int(x1 // self.grid_size) * self.grid_size
        for x in range(start_x, int(x2) + self.grid_size, self.grid_size):
            self.canvas.create_line(x, y1, x, y2, fill="lightgray", tags="grid")

        # Draw horizontal lines
        start_y = int(y1 // self.grid_size) * self.grid_size
        for y in range(start_y, int(y2) + self.grid_size, self.grid_size):
            self.canvas.create_line(x1, y, x2, y, fill="lightgray", tags="grid")

    def update_canvas_display(self):
        """Update the canvas display including grid"""
        self.draw_grid()
    
    def setup_ui(self):
        """Setup the user interface"""
        # Main container
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Create paned window for left/right split
        paned_window = ttk.PanedWindow(main_frame, orient="horizontal")
        paned_window.pack(fill="both", expand=True)
        
        # Left panel for controls
        left_frame = ttk.Frame(paned_window)
        paned_window.add(left_frame, weight=1)
        
        # Right panel for canvas
        right_frame = ttk.Frame(paned_window)
        paned_window.add(right_frame, weight=3)
        
        self.setup_left_panel(left_frame)
        self.setup_canvas_panel(right_frame)
    
    def setup_left_panel(self, parent):
        """Setup the left control panel"""
        # Title
        title_label = ttk.Label(parent, text="PDF Preview Canvas", font=("Arial", 14, "bold"))
        title_label.pack(pady=10)
        
        # File operations
        file_frame = ttk.LabelFrame(parent, text="File Operations")
        file_frame.pack(fill="x", padx=5, pady=5)
        
        ttk.Button(file_frame, text="Add PNG Files", command=self.add_png_files).pack(fill="x", padx=5, pady=2)
        ttk.Button(file_frame, text="Load Project Previews", command=self.load_project_previews).pack(fill="x", padx=5, pady=2)

        # Grid controls
        grid_frame = ttk.LabelFrame(parent, text="Grid & Snap")
        grid_frame.pack(fill="x", padx=5, pady=5)

        # Grid toggle
        self.grid_var = tk.BooleanVar(value=self.show_grid)
        ttk.Checkbutton(grid_frame, text="Show Grid", variable=self.grid_var,
                       command=self.toggle_grid).pack(fill="x", padx=5, pady=2)

        # Snap toggle
        self.snap_var = tk.BooleanVar(value=self.snap_to_grid)
        ttk.Checkbutton(grid_frame, text="Snap to Grid", variable=self.snap_var,
                       command=self.toggle_snap).pack(fill="x", padx=5, pady=2)

        # Grid size
        grid_size_frame = ttk.Frame(grid_frame)
        grid_size_frame.pack(fill="x", padx=5, pady=2)
        ttk.Label(grid_size_frame, text="Grid Size:").pack(side="left")
        self.grid_size_var = tk.IntVar(value=self.grid_size)
        grid_size_spinbox = ttk.Spinbox(grid_size_frame, from_=10, to=100, width=8,
                                       textvariable=self.grid_size_var, command=self.update_grid_size)
        grid_size_spinbox.pack(side="right")

        # Manual size controls
        size_frame = ttk.LabelFrame(parent, text="Manual Size")
        size_frame.pack(fill="x", padx=5, pady=5)

        # Width control
        width_frame = ttk.Frame(size_frame)
        width_frame.pack(fill="x", padx=5, pady=2)
        ttk.Label(width_frame, text="Width:").pack(side="left")
        self.width_var = tk.IntVar(value=200)
        ttk.Entry(width_frame, textvariable=self.width_var, width=8).pack(side="right")

        # Height control
        height_frame = ttk.Frame(size_frame)
        height_frame.pack(fill="x", padx=5, pady=2)
        ttk.Label(height_frame, text="Height:").pack(side="left")
        self.height_var = tk.IntVar(value=150)
        ttk.Entry(height_frame, textvariable=self.height_var, width=8).pack(side="right")

        # Apply button
        ttk.Button(size_frame, text="Apply Size to Selected", command=self.apply_manual_size).pack(fill="x", padx=5, pady=2)

        # Action buttons
        action_frame = ttk.LabelFrame(parent, text="Actions")
        action_frame.pack(fill="x", padx=5, pady=5)

        ttk.Button(action_frame, text="Edit Selected", command=self.edit_selected_measurement).pack(fill="x", padx=5, pady=2)
        ttk.Button(action_frame, text="Delete Selected", command=self.delete_selected).pack(fill="x", padx=5, pady=2)
        
        # Layout operations
        layout_frame = ttk.LabelFrame(parent, text="Layout")
        layout_frame.pack(fill="x", padx=5, pady=5)
        
        ttk.Button(layout_frame, text="Auto Arrange", command=self.auto_arrange).pack(fill="x", padx=5, pady=2)
        ttk.Button(layout_frame, text="Align Left", command=lambda: self.align_items("left")).pack(fill="x", padx=5, pady=2)
        ttk.Button(layout_frame, text="Align Top", command=lambda: self.align_items("top")).pack(fill="x", padx=5, pady=2)
        
        # Export operations
        export_frame = ttk.LabelFrame(parent, text="Export")
        export_frame.pack(fill="x", padx=5, pady=5)
        
        ttk.Button(export_frame, text="Export to PDF", command=self.export_to_pdf).pack(fill="x", padx=5, pady=2)
        ttk.Button(export_frame, text="Save Layout", command=self.save_layout).pack(fill="x", padx=5, pady=2)
        ttk.Button(export_frame, text="Load Layout", command=self.load_layout).pack(fill="x", padx=5, pady=2)
    
    def setup_canvas_panel(self, parent):
        """Setup the canvas panel"""
        # Canvas with scrollbars
        canvas_frame = ttk.Frame(parent)
        canvas_frame.pack(fill="both", expand=True)
        
        # Create canvas with scrollbars
        self.canvas = tk.Canvas(canvas_frame, bg="white", width=self.canvas_width, height=self.canvas_height)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.canvas.yview)
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient="horizontal", command=self.canvas.xview)
        
        self.canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack scrollbars and canvas
        v_scrollbar.pack(side="right", fill="y")
        h_scrollbar.pack(side="bottom", fill="x")
        self.canvas.pack(side="left", fill="both", expand=True)
        
        # Set scroll region
        self.canvas.configure(scrollregion=(0, 0, 1200, 1000))

    def bind_events(self):
        """Bind mouse events for drag and drop functionality"""
        self.canvas.bind("<Button-1>", self.on_canvas_click)
        self.canvas.bind("<B1-Motion>", self.on_canvas_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_canvas_release)
        self.canvas.bind("<Double-Button-1>", self.on_canvas_double_click)

        # Bind scroll events to update grid
        self.canvas.bind("<Configure>", self.on_canvas_configure)

    def on_canvas_configure(self, event):
        """Handle canvas configuration changes (like scrolling)"""
        if self.show_grid:
            self.draw_grid()

    def on_canvas_click(self, event):
        """Handle canvas click events"""
        x = self.canvas.canvasx(event.x)
        y = self.canvas.canvasy(event.y)

        # Check if clicking on a resize handle
        for item in self.selected_items:
            handle_type = item.get_resize_handle_at(x, y)
            if handle_type is not None:
                self.start_resize(item, handle_type, x, y)
                return

        # Check if clicking on an item
        clicked_item = None
        for item in reversed(self.preview_items):  # Check from top to bottom
            if item.contains_point(x, y):
                clicked_item = item
                break

        # Handle selection
        if clicked_item:
            if not (event.state & 0x4):  # Ctrl not pressed
                self.deselect_all()
            self.select_item(clicked_item)
            self.start_drag(clicked_item, x, y)
        else:
            self.deselect_all()

    def on_canvas_drag(self, event):
        """Handle canvas drag events"""
        x = self.canvas.canvasx(event.x)
        y = self.canvas.canvasy(event.y)

        if self.resize_data["item"]:
            self.handle_resize(x, y)
        elif self.drag_data["item"]:
            self.handle_drag(x, y)

    def on_canvas_release(self, event):
        """Handle canvas release events"""
        self.drag_data = {"item": None, "x": 0, "y": 0}
        self.resize_data = {"item": None, "handle": None, "start_x": 0, "start_y": 0}

    def on_canvas_double_click(self, event):
        """Handle canvas double-click events"""
        x = self.canvas.canvasx(event.x)
        y = self.canvas.canvasy(event.y)

        # Check if double-clicking on an item
        for item in reversed(self.preview_items):
            if item.contains_point(x, y):
                self.edit_item(item)
                break

    def start_drag(self, item, x, y):
        """Start dragging an item"""
        self.drag_data = {"item": item, "x": x, "y": y}

    def handle_drag(self, x, y):
        """Handle dragging motion"""
        item = self.drag_data["item"]
        if not item:
            return

        dx = x - self.drag_data["x"]
        dy = y - self.drag_data["y"]

        # Move all selected items
        for selected_item in self.selected_items:
            new_x = selected_item.x + dx
            new_y = selected_item.y + dy

            # Apply snap to grid if enabled
            if self.snap_to_grid:
                new_x, new_y = self.snap_to_grid(new_x, new_y)

            selected_item.move_to(new_x, new_y)

        self.drag_data["x"] = x
        self.drag_data["y"] = y

    def start_resize(self, item, handle_type, x, y):
        """Start resizing an item"""
        self.resize_data = {
            "item": item,
            "handle": handle_type,
            "start_x": x,
            "start_y": y,
            "start_width": item.width,
            "start_height": item.height
        }

    def handle_resize(self, x, y):
        """Handle resizing motion"""
        item = self.resize_data["item"]
        if not item:
            return

        handle_type = self.resize_data["handle"]
        dx = x - self.resize_data["start_x"]
        dy = y - self.resize_data["start_y"]

        start_width = self.resize_data["start_width"]
        start_height = self.resize_data["start_height"]

        # Calculate new dimensions based on handle type
        if handle_type == 0:  # Top-left
            new_width = start_width - dx
            new_height = start_height - dy
            if new_width > 50 and new_height > 50:
                item.move_to(item.x + dx, item.y + dy)
                item.resize_to(new_width, new_height)
        elif handle_type == 1:  # Top-right
            new_width = start_width + dx
            new_height = start_height - dy
            if new_width > 50 and new_height > 50:
                item.move_to(item.x, item.y + dy)
                item.resize_to(new_width, new_height)
        elif handle_type == 2:  # Bottom-left
            new_width = start_width - dx
            new_height = start_height + dy
            if new_width > 50 and new_height > 50:
                item.move_to(item.x + dx, item.y)
                item.resize_to(new_width, new_height)
        elif handle_type == 3:  # Bottom-right
            new_width = start_width + dx
            new_height = start_height + dy
            if new_width > 50 and new_height > 50:
                item.resize_to(new_width, new_height)

    def select_item(self, item):
        """Select an item"""
        if item not in self.selected_items:
            self.selected_items.append(item)
            item.set_selected(True)

    def deselect_item(self, item):
        """Deselect an item"""
        if item in self.selected_items:
            self.selected_items.remove(item)
            item.set_selected(False)

    def select_all(self):
        """Select all items"""
        for item in self.preview_items:
            self.select_item(item)

    def deselect_all(self):
        """Deselect all items"""
        for item in self.selected_items[:]:  # Copy list to avoid modification during iteration
            self.deselect_item(item)

    def toggle_grid(self):
        """Toggle grid display"""
        self.show_grid = self.grid_var.get()
        self.update_canvas_display()

    def toggle_snap(self):
        """Toggle snap to grid"""
        self.snap_to_grid = self.snap_var.get()

    def update_grid_size(self):
        """Update grid size"""
        self.grid_size = self.grid_size_var.get()
        if self.show_grid:
            self.update_canvas_display()

    def apply_manual_size(self):
        """Apply manual size to selected items"""
        if not self.selected_items:
            messagebox.showwarning("No Selection", "Please select items to resize")
            return

        width = self.width_var.get()
        height = self.height_var.get()

        if width <= 0 or height <= 0:
            messagebox.showerror("Invalid Size", "Width and height must be positive numbers")
            return

        for item in self.selected_items:
            item.resize_to(width, height)

    def edit_selected_measurement(self):
        """Edit the selected measurement in appropriate plotting app"""
        if not self.selected_items:
            messagebox.showwarning("Warning", "Please select a measurement to edit")
            return

        if len(self.selected_items) > 1:
            messagebox.showwarning("Warning", "Please select only one measurement to edit")
            return

        if not self.project_overview:
            messagebox.showwarning("Warning", "Editing not available - no project overview reference")
            return

        # Get the selected item
        selected_item = self.selected_items[0]

        # Extract measurement path from image path
        image_path = selected_item.image_path
        measurement_path = os.path.dirname(image_path)

        # Check if it's a valid measurement (has plot_config.json)
        config_file = os.path.join(measurement_path, "plot_config.json")
        if not os.path.exists(config_file):
            messagebox.showwarning("Warning", "Selected item is not a valid measurement")
            return

        try:
            # Load plot config
            with open(config_file, 'r') as f:
                plot_config = json.load(f)

            # Load data
            data_dir = os.path.join(measurement_path, "Data")
            data_files = []
            if os.path.exists(data_dir):
                data_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]

            if not data_files:
                messagebox.showerror("Error", "No data files found in measurement")
                return

            # Load first data file
            data_file = os.path.join(data_dir, data_files[0])

            import pandas as pd
            data = pd.read_csv(data_file)

            # Detect dimensionality using project overview method
            dimensionality = self.project_overview.detect_data_dimensionality(data_file)

            # Add project directory to config
            enhanced_config = plot_config.copy()
            enhanced_config['project_directory'] = self.project_overview.project_directory

            # Get measurement name for window title
            measurement_name = os.path.basename(measurement_path)

            # Launch appropriate plotting window based on dimensionality
            if dimensionality == "3D":
                from PlottingApp3D import Plot3DWindow

                # Create new window for 3D plotting
                plot_window = tk.Toplevel(self.window)
                plot_window.title(f"3D Plot - {measurement_name}")
                plot_window.geometry("1200x800")

                # Create 3D plotting app
                Plot3DWindow(plot_window, data, enhanced_config, data_files[0])

            else:  # 2D
                from PlottingApp2D import Plot2DWindow

                # Create new window for 2D plotting
                plot_window = tk.Toplevel(self.window)
                plot_window.title(f"2D Plot - {measurement_name}")
                plot_window.geometry("1400x900")

                # Create 2D plotting app
                Plot2DWindow(plot_window, data, enhanced_config, data_files[0])

            # Set up window closing handler to refresh preview if needed
            def on_plot_window_close():
                plot_window.destroy()
                # Optionally refresh the preview image here
                self.refresh_preview_image(selected_item)

            plot_window.protocol("WM_DELETE_WINDOW", on_plot_window_close)

        except Exception as e:
            messagebox.showerror("Error", f"Could not open measurement for editing: {e}")
            import traceback
            traceback.print_exc()

    def refresh_preview_image(self, item):
        """Refresh the preview image for an item after editing"""
        try:
            # Reload the image from disk
            item.load_image()
            item.update_display_image()

            # Recreate canvas item
            if item.image_id:
                self.canvas.delete(item.image_id)
            item.create_canvas_item()

            # Update selection display if item is selected
            if item in self.selected_items:
                item.update_selection_display()
                item.update_resize_handles()

        except Exception as e:
            print(f"Could not refresh preview image: {e}")

    def delete_selected(self):
        """Delete selected items"""
        if not self.selected_items:
            return

        result = messagebox.askyesno("Confirm Delete",
                                   f"Delete {len(self.selected_items)} selected item(s)?")
        if result:
            for item in self.selected_items[:]:
                self.remove_item(item)

    def remove_item(self, item):
        """Remove an item from the canvas"""
        item.cleanup()
        if item in self.preview_items:
            self.preview_items.remove(item)
        if item in self.selected_items:
            self.selected_items.remove(item)



    def add_png_files(self):
        """Add PNG files to the canvas"""
        file_paths = filedialog.askopenfilenames(
            title="Select PNG Files",
            filetypes=[("PNG files", "*.png"), ("All files", "*.*")]
        )

        if file_paths:
            self.add_images_to_canvas(file_paths)

    def add_images_to_canvas(self, image_paths):
        """Add multiple images to the canvas"""
        start_x, start_y = 50, 50
        spacing = 20

        for i, image_path in enumerate(image_paths):
            # Calculate position (simple grid layout)
            col = i % 3
            row = i // 3
            x = start_x + col * (200 + spacing)
            y = start_y + row * (150 + spacing)

            item = PreviewItem(self.canvas, image_path, x, y)
            self.preview_items.append(item)

    def load_project_previews(self):
        """Load PNG previews from project structure"""
        project_dir = filedialog.askdirectory(
            title="Select Project Directory"
        )

        if not project_dir:
            return

        # Find all preview PNG files in project structure
        preview_files = []
        for root, dirs, files in os.walk(project_dir):
            for file in files:
                if file.endswith("_preview.png"):
                    preview_files.append(os.path.join(root, file))

        if preview_files:
            self.add_images_to_canvas(preview_files)
            messagebox.showinfo("Success", f"Loaded {len(preview_files)} preview images")
        else:
            messagebox.showwarning("No Previews", "No preview images found in selected directory")

    def auto_arrange(self):
        """Auto-arrange items in a grid"""
        if not self.preview_items:
            return

        cols = 3
        spacing = 20
        start_x, start_y = 50, 50

        for i, item in enumerate(self.preview_items):
            col = i % cols
            row = i // cols
            x = start_x + col * (item.width + spacing)
            y = start_y + row * (item.height + spacing)
            item.move_to(x, y)

    def align_items(self, alignment):
        """Align selected items"""
        if len(self.selected_items) < 2:
            messagebox.showwarning("Selection", "Please select at least 2 items to align")
            return

        if alignment == "left":
            min_x = min(item.x for item in self.selected_items)
            for item in self.selected_items:
                item.move_to(min_x, item.y)
        elif alignment == "top":
            min_y = min(item.y for item in self.selected_items)
            for item in self.selected_items:
                item.move_to(item.x, min_y)

    def edit_item(self, item):
        """Edit item properties (placeholder for future functionality)"""
        messagebox.showinfo("Edit Item", f"Edit functionality for:\n{os.path.basename(item.image_path)}")

    def export_to_pdf(self):
        """Export canvas layout to PDF"""
        if not REPORTLAB_AVAILABLE:
            messagebox.showerror("Error", "ReportLab not available. Cannot export to PDF.")
            return

        if not self.preview_items:
            messagebox.showwarning("No Items", "No items to export")
            return

        filename = filedialog.asksaveasfilename(
            title="Save PDF",
            defaultextension=".pdf",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )

        if not filename:
            return

        try:
            # Create PDF
            pdf = pdf_canvas.Canvas(filename, pagesize=A4)
            page_width, page_height = A4

            # Calculate scale factor to fit canvas to page
            canvas_width = max(item.x + item.width for item in self.preview_items) if self.preview_items else 800
            canvas_height = max(item.y + item.height for item in self.preview_items) if self.preview_items else 600

            scale_x = (page_width - 100) / canvas_width  # Leave 50pt margin on each side
            scale_y = (page_height - 100) / canvas_height
            scale = min(scale_x, scale_y, 1.0)  # Don't scale up

            # Add items to PDF
            for item in self.preview_items:
                if os.path.exists(item.image_path):
                    # Calculate position and size on PDF
                    pdf_x = 50 + item.x * scale
                    pdf_y = page_height - 50 - (item.y + item.height) * scale  # Flip Y coordinate
                    pdf_width = item.width * scale
                    pdf_height = item.height * scale

                    # Add image to PDF
                    pdf.drawImage(item.image_path, pdf_x, pdf_y, pdf_width, pdf_height)

            pdf.save()
            messagebox.showinfo("Success", f"PDF exported to {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to export PDF: {str(e)}")

    def save_layout(self):
        """Save current layout to JSON file"""
        if not self.preview_items:
            messagebox.showwarning("No Items", "No items to save")
            return

        filename = filedialog.asksaveasfilename(
            title="Save Layout",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if not filename:
            return

        try:
            layout_data = {
                "canvas_size": {"width": self.canvas_width, "height": self.canvas_height},
                "items": []
            }

            for item in self.preview_items:
                item_data = {
                    "image_path": item.image_path,
                    "x": item.x,
                    "y": item.y,
                    "width": item.width,
                    "height": item.height
                }
                layout_data["items"].append(item_data)

            with open(filename, 'w') as f:
                json.dump(layout_data, f, indent=2)

            messagebox.showinfo("Success", f"Layout saved to {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save layout: {str(e)}")

    def load_layout(self):
        """Load layout from JSON file"""
        filename = filedialog.askopenfilename(
            title="Load Layout",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if not filename:
            return

        try:
            with open(filename, 'r') as f:
                layout_data = json.load(f)

            # Clear current items
            self.clear_canvas()

            # Load items from layout
            for item_data in layout_data.get("items", []):
                if os.path.exists(item_data["image_path"]):
                    item = PreviewItem(
                        self.canvas,
                        item_data["image_path"],
                        item_data["x"],
                        item_data["y"],
                        item_data["width"],
                        item_data["height"]
                    )
                    self.preview_items.append(item)
                else:
                    print(f"Warning: Image not found: {item_data['image_path']}")

            messagebox.showinfo("Success", f"Layout loaded from {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load layout: {str(e)}")

    def run(self):
        """Run the application"""
        self.window.mainloop()


def main():
    """Main function"""
    if not PIL_AVAILABLE:
        messagebox.showwarning("Warning",
                             "PIL (Pillow) is not available. Image functionality will be limited.\n"
                             "Install with: pip install Pillow")

    if not REPORTLAB_AVAILABLE:
        messagebox.showwarning("Warning",
                             "ReportLab is not available. PDF export functionality will be limited.\n"
                             "Install with: pip install reportlab")

    app = PDFPreviewCanvas()
    app.run()


if __name__ == "__main__":
    main()
