#!/usr/bin/env python3
"""
Test script for PDF Preview Canvas Manager
Creates sample PNG files and demonstrates the canvas functionality.
"""

import os
import tempfile
from pathlib import Path

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("Warning: PIL not available. Cannot create test images.")

def create_test_images():
    """Create sample PNG images for testing"""
    if not PIL_AVAILABLE:
        print("PIL not available - cannot create test images")
        return []
    
    # Create temporary directory for test images
    test_dir = tempfile.mkdtemp(prefix="pdf_canvas_test_")
    print(f"Creating test images in: {test_dir}")
    
    test_images = []
    
    # Create different types of test images
    test_configs = [
        {"name": "Sample_Plot_1_preview.png", "color": "lightblue", "text": "Sample Plot 1"},
        {"name": "Sample_Plot_2_preview.png", "color": "lightgreen", "text": "Sample Plot 2"},
        {"name": "Sample_Plot_3_preview.png", "color": "lightcoral", "text": "Sample Plot 3"},
        {"name": "Data_Analysis_preview.png", "color": "lightyellow", "text": "Data Analysis"},
        {"name": "Measurement_A_preview.png", "color": "lightpink", "text": "Measurement A"},
    ]
    
    for config in test_configs:
        # Create image
        image = Image.new('RGB', (300, 200), color=config["color"])
        draw = ImageDraw.Draw(image)
        
        # Add border
        draw.rectangle([5, 5, 295, 195], outline='black', width=2)
        
        # Add title text
        try:
            # Try to use a default font
            font = ImageFont.load_default()
        except:
            font = None
        
        # Calculate text position
        text = config["text"]
        if font:
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
        else:
            text_width = len(text) * 6  # Rough estimate
            text_height = 11
        
        text_x = (300 - text_width) // 2
        text_y = (200 - text_height) // 2
        
        draw.text((text_x, text_y), text, fill='black', font=font)
        
        # Add some sample plot elements
        if "Plot" in config["text"]:
            # Add some sample data points
            for i in range(10):
                x = 50 + i * 20
                y = 150 - (i % 3) * 20
                draw.ellipse([x-3, y-3, x+3, y+3], fill='red')
            
            # Add axes
            draw.line([50, 150, 250, 150], fill='black', width=2)  # X-axis
            draw.line([50, 50, 50, 150], fill='black', width=2)   # Y-axis
        
        # Save image
        image_path = os.path.join(test_dir, config["name"])
        image.save(image_path)
        test_images.append(image_path)
        print(f"Created: {config['name']}")
    
    return test_images

def create_project_structure():
    """Create a sample project structure with preview images"""
    if not PIL_AVAILABLE:
        print("PIL not available - cannot create project structure")
        return None
    
    # Create temporary project directory
    project_dir = tempfile.mkdtemp(prefix="sample_project_")
    print(f"Creating sample project structure in: {project_dir}")
    
    # Create project structure
    projects = {
        "Project_A": ["Measurement_1", "Measurement_2"],
        "Project_B": ["Sample_Data", "Calibration"],
    }
    
    for project_name, measurements in projects.items():
        project_path = os.path.join(project_dir, project_name)
        os.makedirs(project_path, exist_ok=True)
        
        for measurement in measurements:
            measurement_path = os.path.join(project_path, measurement)
            os.makedirs(measurement_path, exist_ok=True)
            
            # Create preview image
            image = Image.new('RGB', (250, 180), color='lightsteelblue')
            draw = ImageDraw.Draw(image)
            
            # Add border and text
            draw.rectangle([2, 2, 248, 178], outline='darkblue', width=2)
            
            text = f"{project_name}\n{measurement}"
            lines = text.split('\n')
            y_offset = 70
            for line in lines:
                bbox = draw.textbbox((0, 0), line)
                text_width = bbox[2] - bbox[0]
                x = (250 - text_width) // 2
                draw.text((x, y_offset), line, fill='darkblue')
                y_offset += 20
            
            # Save preview
            preview_path = os.path.join(measurement_path, f"{measurement}_preview.png")
            image.save(preview_path)
            print(f"Created: {preview_path}")
    
    return project_dir

def main():
    """Main test function"""
    print("PDF Preview Canvas Test")
    print("=" * 40)
    
    if not PIL_AVAILABLE:
        print("PIL not available. Please install Pillow to run tests:")
        print("pip install Pillow")
        return
    
    # Create test images
    print("\n1. Creating individual test images...")
    test_images = create_test_images()
    
    # Create project structure
    print("\n2. Creating sample project structure...")
    project_dir = create_project_structure()
    
    print("\n" + "=" * 40)
    print("Test setup complete!")
    print("\nTo test the PDF Preview Canvas:")
    print("1. Run: python PDFPreviewCanvas.py")
    print("2. Use 'Add PNG Files' to load individual images from:")
    for img in test_images[:3]:  # Show first 3
        print(f"   - {img}")
    if len(test_images) > 3:
        print(f"   ... and {len(test_images) - 3} more")
    
    if project_dir:
        print("3. Use 'Load Project Previews' to load from project structure:")
        print(f"   - {project_dir}")
    
    print("\nFeatures to test:")
    print("- Drag items around the canvas")
    print("- Select items (click, Ctrl+click for multiple)")
    print("- Resize items using the blue handles")
    print("- Use layout tools (Auto Arrange, Align)")
    print("- Export to PDF")
    print("- Save/Load layout configurations")
    
    # Try to launch the application
    try:
        print("\nAttempting to launch PDF Preview Canvas...")
        import PDFPreviewCanvas
        app = PDFPreviewCanvas.PDFPreviewCanvas()
        
        # Add test images automatically
        if test_images:
            app.add_images_to_canvas(test_images[:3])  # Add first 3 images
        
        app.run()
    except ImportError as e:
        print(f"Could not import PDFPreviewCanvas: {e}")
        print("Make sure PDFPreviewCanvas.py is in the same directory")
    except Exception as e:
        print(f"Error launching application: {e}")

if __name__ == "__main__":
    main()
